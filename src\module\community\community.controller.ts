import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { CommunityService } from './community.service';
import { UserLoginJwtGuard } from 'src/module/auth/guards/firebase-auth.guard';
import { GetUserSession } from 'src/shared/decorators/user-session.decorator';
import { UserSessionType } from 'src/shared/types/user-session.type';
import {
  CreateCommunityDto,
  UpdateCommunityDto,
  UpdateCommunityStatusDto,
  JoinCommunityDto,
} from './dto/community.dto';

@ApiTags('Community')
@ApiBearerAuth()
@UseGuards(UserLoginJwtGuard)
@Controller('community')
export class CommunityController {
  constructor(private readonly communityService: CommunityService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new community (Owner only)' })
  async createCommunity(
    @Body() dto: CreateCommunityDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.createCommunity(dto, sessionData);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update community details (Owner only)' })
  async updateCommunity(
    @Param('id') communityId: string,
    @Body() dto: UpdateCommunityDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.updateCommunity(communityId, dto, sessionData);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete community (Owner only)' })
  async deleteCommunity(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.deleteCommunity(communityId, sessionData);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update community status (Owner only)' })
  async updateCommunityStatus(
    @Param('id') communityId: string,
    @Body() dto: UpdateCommunityStatusDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.updateCommunityStatus(communityId, dto, sessionData);
  }

  @Get('owner')
  @ApiOperation({ summary: 'Get communities created by owner' })
  async getOwnerCommunities(@GetUserSession() sessionData: UserSessionType) {
    return this.communityService.getOwnerCommunities(sessionData);
  }

  @Post('join')
  @ApiOperation({ summary: 'Join a community (Member)' })
  async joinCommunity(
    @Body() dto: JoinCommunityDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.joinCommunity(dto, sessionData);
  }

  @Get('purchases')
  @ApiOperation({ summary: 'Get member purchases/joined communities' })
  async getMemberPurchases(@GetUserSession() sessionData: UserSessionType) {
    return this.communityService.getMemberPurchases(sessionData);
  }

  @Get(':id/members')
  @ApiOperation({ summary: 'Get community members (Owner only)' })
  async getCommunityMembers(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.getCommunityMembers(communityId, sessionData);
  }

  @Get('available')
  @ApiOperation({ summary: 'Get available communities for members to join' })
  async getAvailableCommunities(@GetUserSession() sessionData: UserSessionType) {
    return this.communityService.getAvailableCommunities(sessionData);
  }

  @Get(':id/details')
  @ApiOperation({ summary: 'Get community details' })
  async getCommunityDetails(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.getCommunityDetails(communityId, sessionData);
  }

  @Get(':id/subscription-status')
  @ApiOperation({ summary: 'Check user subscription status for a community' })
  async checkSubscriptionStatus(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.checkSubscriptionStatus(communityId, sessionData);
  }
}