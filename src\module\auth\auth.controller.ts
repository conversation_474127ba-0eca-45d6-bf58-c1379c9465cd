import {
  Controller,
  Post,
  Patch,
  Delete,
  Body,
  Query,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  VerifyEmailDto,
  SignupDto,
  LoginDto,
  SendVerificationEmailDto,
  CheckEmailDto,
} from './dto/auth.dto';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import {
  GetUserRequestHeader,
  GetUserSession,
} from 'src/shared/decorators/user-session.decorator';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('user-exists')
  @ApiOperation({ summary: `Check user exists or not` })
  async checkEmail(@Body() dto: CheckEmailDto) {
    return this.authService.checkEmailExists(dto.email);
  }

  @Post('send-verification-email')
  @ApiOperation({ summary: 'Send verification email' })
  async sendVerificationEmail(
    @Body() sendVerificationEmailDto: SendVerificationEmailDto,
  ) {
    return this.authService.sendVerificationEmail(sendVerificationEmailDto);
  }

  @Patch('verify-email')
  @ApiOperation({ summary: 'Verify email with OTP' })
  async verifyEmail(@Body() verifyEmailDto: VerifyEmailDto) {
    return this.authService.verifyEmail(verifyEmailDto);
  }

  @Post('signup')
  @ApiOperation({ summary: `New User Sign-Up` })
  @ApiBearerAuth()
  async signup(
    @Body() signupDto: SignupDto,
    @GetUserRequestHeader() headers: any,
  ) {
    return await this.authService.signup({
      signupDto,
      headers,
    });
  }

  @Post('login')
  @ApiOperation({ summary: 'User Login' })
  @ApiBearerAuth()
  async login(
    @Body() loginDto: LoginDto,
    @GetUserRequestHeader() headers: any,
  ) {
    return await this.authService.login({
      loginDto,
      headers,
    });
  }

  @Delete('logout')
  @ApiOperation({ summary: 'Logout user' })
  @ApiBearerAuth()
  async logout(
    @Query('sessionId') sessionId: string,
    @GetUserRequestHeader() headers: any,
  ) {
    return this.authService.logout({ sessionId, headers });
  }

  @Delete('delete')
  @ApiOperation({ summary: 'Delete user' })
  @ApiBearerAuth()
  async deleteUser(@GetUserRequestHeader() headers: any) {
    return this.authService.deleteUser(headers);
  }
}
