import { Global, Module } from '@nestjs/common';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { CommunityCoreService } from './community-core.service';
import { PrismaModule } from 'src/shared/module/prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [CommunityCoreService, PrismaService],
  exports: [CommunityCoreService],
})
export class CommunityCoreModule {}
