import {
  <PERSON>,
  Post,
  Get,
  Patch,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { SubscriptionService } from './subscription.service';
import { UserLoginJwtGuard } from 'src/module/auth/guards/firebase-auth.guard';
import { GetUserSession } from 'src/shared/decorators/user-session.decorator';
import { UserSessionType } from 'src/shared/types/user-session.type';
import { RenewSubscriptionDto } from './dto/subscription.dto';

@ApiTags('Subscription')
@ApiBearerAuth()
@UseGuards(UserLoginJwtGuard)
@Controller('subscription')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Post(':communityId/renew')
  @ApiOperation({ summary: 'Renew subscription for a community' })
  async renewSubscription(
    @Param('communityId') communityId: string,
    @Body() dto: RenewSubscriptionDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.subscriptionService.renewSubscription(
      communityId,
      dto.receiptData,
      dto.platform,
      sessionData,
      dto.packageName,
    );
  }

  @Patch(':communityId/cancel')
  @ApiOperation({ summary: 'Cancel subscription for a community' })
  async cancelSubscription(
    @Param('communityId') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.subscriptionService.cancelSubscription(communityId, sessionData);
  }

  @Get(':communityId/validate')
  @ApiOperation({ summary: 'Validate and sync subscription with app store' })
  async validateAndSyncSubscription(
    @Param('communityId') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.subscriptionService.validateAndSyncSubscription(communityId, sessionData);
  }

  @Get('my-subscriptions')
  @ApiOperation({ summary: 'Get user subscriptions' })
  async getUserSubscriptions(@GetUserSession() sessionData: UserSessionType) {
    return this.subscriptionService.getUserSubscriptions(sessionData);
  }

  @Post('check-expired')
  @ApiOperation({ summary: 'Check and update expired subscriptions (Admin only)' })
  async checkExpiredSubscriptions() {
    return this.subscriptionService.checkExpiredSubscriptions();
  }
}
