import { Module } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './module/auth/auth.module';
import configuration from 'config/configuration';
import { PrismaModule } from './shared/module/prisma/prisma.module';
import { UploadModule } from './module/upload/upload.module';
import { ProfileModule } from './module/profile/profile.module';
import { CommunityModule } from './module/community/community.module';
import { IAPModule } from './module/iap/iap.module';
import { SubscriptionModule } from './module/subscription/subscription.module';
import { WebhookModule } from './module/webhook/webhook.module';

@Module({
  imports: [
    ThrottlerModule.forRoot([{ ttl: 60, limit: 10 }]),
    ConfigModule.forRoot({
      envFilePath: '.env',
      load: [configuration],
      cache: true,
      isGlobal: true,
    }),
    ConfigModule.forRoot({ isGlobal: true }),
    PrismaModule,
    UploadModule,
    AuthModule,
    ProfileModule,
    CommunityModule,
    IAPModule,
    SubscriptionModule,
    WebhookModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
