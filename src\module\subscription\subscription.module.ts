import { Module } from '@nestjs/common';
import { SubscriptionController } from './subscription.controller';
import { SubscriptionService } from './subscription.service';
import { MemberPurchaseCoreModule } from 'src/core/member-purchase-core/member-purchase-core.module';
import { CommunityCoreModule } from 'src/core/community-core/community-core.module';
import { IAPModule } from '../iap/iap.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MemberPurchaseCoreModule,
    CommunityCoreModule,
    IAPModule,
    AuthModule,
  ],
  controllers: [SubscriptionController],
  providers: [SubscriptionService],
  exports: [SubscriptionService],
})
export class SubscriptionModule {}
