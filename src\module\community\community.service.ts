import {
  Injectable,
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { USER_TYPE, COMMUNITY_STATUS } from '@prisma/client';
import { CommunityCoreService } from 'src/core/community-core/community-core.service';
import { PriceModelCoreService } from 'src/core/price-model-core/price-model-core.service';
import { MemberPurchaseCoreService } from 'src/core/member-purchase-core/member-purchase-core.service';
import { MemberDetailsCoreService } from 'src/core/member-details-core/member-details-core.service';
import { IAPService } from 'src/shared/services/iap.service';
import { UserSessionType } from 'src/shared/types/user-session.type';
import {
  CreateCommunityDto,
  UpdateCommunityDto,
  UpdateCommunityStatusDto,
  JoinCommunityDto,
} from './dto/community.dto';
import {
  communityMessage,
  memberPurchaseMessage,
  iapMessage,
} from 'src/shared/keys/helper.key';

@Injectable()
export class CommunityService {
  constructor(
    private readonly communityCoreService: CommunityCoreService,
    private readonly priceModelCoreService: PriceModelCoreService,
    private readonly memberPurchaseCoreService: MemberPurchaseCoreService,
    private readonly memberDetailsCoreService: MemberDetailsCoreService,
    private readonly iapService: IAPService,
  ) {}

  async createCommunity(dto: CreateCommunityDto, sessionData: UserSessionType) {
    // Only owners can create communities
    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    let priceId: string | undefined;

    // Handle pricing if not free
    if (!dto.isFree && dto.priceAmount && dto.priceInterval) {
      const priceModel = await this.priceModelCoreService.createPriceModel(
        dto.priceAmount,
        dto.priceInterval,
        dto.priceCurrency || 'USD',
      );
      priceId = priceModel.id;
    }

    // Create community with PENDING status
    const community = await this.communityCoreService.create({
      data: {
        userId: sessionData.user.id,
        name: dto.name,
        description: dto.description,
        image: dto.image,
        banner: dto.banner,
        videoUrl: dto.videoUrl,
        isFree: dto.isFree || true,
        priceId: priceId,
        maxMembers: dto.maxMembers,
        communityStatus: COMMUNITY_STATUS.PENDING,
      },
    });

    // If not free, create products in app stores
    if (!dto.isFree && dto.priceAmount && dto.priceInterval) {
      await this.createAppStoreProducts(community.id, dto);
    }

    return {
      status: true,
      message: communityMessage.COMMUNITY_CREATED,
      community,
    };
  }

  private async createAppStoreProducts(communityId: string, dto: CreateCommunityDto) {
    try {
      const productId = `community_${communityId}`;

      // Create Google Play product
      const googleProduct = await this.iapService.createGooglePlayProduct(
        process.env.GOOGLE_PLAY_PACKAGE_NAME || '',
        productId,
        dto.priceAmount!,
        dto.priceCurrency || 'USD',
        dto.priceInterval!,
      );

      // Create Apple product structure
      const appleProduct = await this.iapService.createAppleProduct(
        process.env.APPLE_BUNDLE_ID || '',
        productId,
        dto.name,
        dto.description || '',
        dto.priceAmount!.toString(),
        dto.priceCurrency || 'USD',
        dto.priceInterval!,
      );

      // Update community with product IDs
      await this.communityCoreService.update({
        where: { id: communityId },
        data: {
          googleProductId: googleProduct?.productId,
          appleProductId: appleProduct?.productId,
        },
      });
    } catch (error) {
      console.error('Failed to create app store products:', error);
      // Don't throw error as community is already created
    }
  }

  async updateCommunity(
    communityId: string,
    dto: UpdateCommunityDto,
    sessionData: UserSessionType,
  ) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    // Check if user owns the community
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    let priceId = community.priceId;

    // Handle pricing changes
    if (dto.isFree !== undefined || dto.priceAmount || dto.priceInterval) {
      if (!dto.isFree && dto.priceAmount && dto.priceInterval) {
        const priceModel = await this.priceModelCoreService.createPriceModel(
          dto.priceAmount,
          dto.priceInterval,
          dto.priceCurrency || 'USD',
        );
        priceId = priceModel.id;
      } else if (dto.isFree) {
        priceId = null;
      }
    }

    const updatedCommunity = await this.communityCoreService.update({
      where: { id: communityId },
      data: {
        name: dto.name,
        description: dto.description,
        image: dto.image,
        banner: dto.banner,
        videoUrl: dto.videoUrl,
        isFree: dto.isFree,
        priceId: priceId,
        maxMembers: dto.maxMembers,
      },
    });

    return {
      status: true,
      message: communityMessage.COMMUNITY_UPDATED,
      community: updatedCommunity,
    };
  }

  async deleteCommunity(communityId: string, sessionData: UserSessionType) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    // Check if user owns the community
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    // Soft delete the community
    await this.communityCoreService.update({
      where: { id: communityId },
      data: { isDeleted: true },
    });

    // TODO: Handle cleanup of app store products and active subscriptions

    return {
      status: true,
      message: communityMessage.COMMUNITY_DELETED,
    };
  }

  async updateCommunityStatus(
    communityId: string,
    dto: UpdateCommunityStatusDto,
    sessionData: UserSessionType,
  ) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    // Check if user owns the community
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    const updatedCommunity = await this.communityCoreService.update({
      where: { id: communityId },
      data: { communityStatus: dto.status },
    });

    return {
      status: true,
      message: communityMessage.COMMUNITY_STATUS_UPDATED,
      community: updatedCommunity,
    };
  }

  async getOwnerCommunities(sessionData: UserSessionType) {
    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    const communities = await this.communityCoreService.findMany({
      where: {
        userId: sessionData.user.id,
        isDeleted: false,
      },
      include: {
        price: true,
        _count: {
          select: {
            MemberPurchase: {
              where: { status: 'active' },
            },
          },
        },
      },
    });

    return {
      status: true,
      communities,
    };
  }

  async joinCommunity(dto: JoinCommunityDto, sessionData: UserSessionType) {
    const community = await this.communityCoreService.findUnique({
      where: { id: dto.communityId },
      include: { price: true },
    });

    // Check if community is active
    if (community.communityStatus !== COMMUNITY_STATUS.ACTIVE) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_ACTIVE);
    }

    // Check if user already joined
    const existingPurchase = await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
      sessionData.user.id,
      dto.communityId,
    );

    if (existingPurchase) {
      throw new BadRequestException(communityMessage.COMMUNITY_ALREADY_JOINED);
    }

    // Handle paid community
    if (!community.isFree) {
      if (!dto.receiptData || !dto.platform) {
        throw new BadRequestException(communityMessage.COMMUNITY_PURCHASE_REQUIRED);
      }

      // Validate receipt
      const validationResult = await this.validatePurchaseReceipt(
        dto.platform,
        community.googleProductId || community.appleProductId || '',
        dto.receiptData,
        dto.packageName,
      );

      if (!validationResult.isValid) {
        throw new BadRequestException(memberPurchaseMessage.PURCHASE_INVALID);
      }

      // Create purchase record
      await this.memberPurchaseCoreService.create({
        data: {
          memberId: sessionData.user.id,
          communityId: dto.communityId,
          productId: validationResult.productId || '',
          platform: dto.platform,
          status: 'active',
          expiresAt: validationResult.expirationDate,
        },
      });
    }

    // Create or update member details
    await this.memberDetailsCoreService.createOrUpdateMemberDetails(
      sessionData.user.id,
      dto.communityId,
    );

    return {
      status: true,
      message: 'Successfully joined community',
    };
  }

  private async validatePurchaseReceipt(
    platform: 'android' | 'ios',
    productId: string,
    receiptData: string,
    packageName?: string,
  ) {
    if (platform === 'android') {
      return this.iapService.validateGooglePlayReceipt(
        packageName || '',
        productId,
        receiptData,
      );
    } else {
      return this.iapService.validateAppleReceipt(receiptData);
    }
  }

  async getMemberPurchases(sessionData: UserSessionType) {
    const purchases = await this.memberPurchaseCoreService.findPurchasesByUser(
      sessionData.user.id,
    );

    return {
      status: true,
      purchases,
    };
  }

  async getCommunityMembers(communityId: string, sessionData: UserSessionType) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    // Check if user owns the community
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    const purchases = await this.memberPurchaseCoreService.findPurchasesByCommunity(
      communityId,
    );

    return {
      status: true,
      members: purchases,
    };
  }

  async getAvailableCommunities(sessionData: UserSessionType) {
    const communities = await this.communityCoreService.findMany({
      where: {
        communityStatus: COMMUNITY_STATUS.ACTIVE,
        isDeleted: false,
      },
      include: {
        price: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profileImage: true,
          },
        },
        _count: {
          select: {
            MemberPurchase: {
              where: { status: 'active' },
            },
          },
        },
      },
    });

    // Filter out communities user already joined
    const userPurchases = await this.memberPurchaseCoreService.findPurchasesByUser(
      sessionData.user.id,
    );
    const joinedCommunityIds = userPurchases.map(p => p.communityId);

    const availableCommunities = communities.filter(
      community => !joinedCommunityIds.includes(community.id),
    );

    return {
      status: true,
      communities: availableCommunities,
    };
  }

  async getCommunityDetails(communityId: string, sessionData: UserSessionType) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
      include: {
        price: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profileImage: true,
          },
        },
        _count: {
          select: {
            MemberPurchase: {
              where: { status: 'active' },
            },
          },
        },
      },
    });

    // Check if user has access to this community
    const userPurchase = await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
      sessionData.user.id,
      communityId,
    );

    const hasAccess = community.isFree || userPurchase || community.userId === sessionData.user.id;

    return {
      status: true,
      community,
      hasAccess,
      userPurchase,
    };
  }

  async checkSubscriptionStatus(communityId: string, sessionData: UserSessionType) {
    const purchase = await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
      sessionData.user.id,
      communityId,
    );

    if (!purchase) {
      return {
        status: true,
        isSubscribed: false,
        message: 'No active subscription found',
      };
    }

    // Check if subscription is still valid
    const isActive = purchase.status === 'active' &&
      (!purchase.expiresAt || purchase.expiresAt > new Date());

    return {
      status: true,
      isSubscribed: isActive,
      purchase,
      expiresAt: purchase.expiresAt,
    };
  }
}