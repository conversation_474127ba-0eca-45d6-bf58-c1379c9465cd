generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ================================================================= Enums =================================================================

enum STATUS {
  ENABLED
  DISABLED
}

enum COMMUNITY_STATUS {
  ACTIVE
  PENDING
}

enum USER_TYPE {
  OWNER
  MEMBER
}

enum AUTH_TYPE {
  EMAIL
  GOOGLE
  APPLE
}

enum PLATFORM {
  ANDROID
  IOS
}

enum WORKOUT_TYPE {
  WEIGHT_TRAINING
  RUNNING
  CYCLING
  CUSTOM_WORKOUT
}

enum REP_TYPE {
  RANGE
  MAX
}

enum CHANNEL_PERMISSION {
  EVERYONE
  OWNER
}

enum MEAL_TYPE {
  BREAKFAST
  LUNCH
  DINNER
  SNACK
}

enum MEDIA_CATEGORY {
  TRAINING
  NUTRITION
  MINDSET
  RECOVERY
  OTHER
}

// ================================================================= Models =================================================================

model PriceModel {
  id       String @id @default(uuid())
  amount   Float
  currency String @default("USD")
  interval String

  // Relations
  communities Community[]

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model MemberDetails {
  id     String @id @default(uuid())
  userId String @unique

  // Member specific fields
  joinedAt        DateTime         @default(now())
  membershipType  String?
  memberPurchases MemberPurchase[]

  status      STATUS     @default(ENABLED)
  isDeleted   Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @default(now()) @updatedAt
  Community   Community? @relation(fields: [communityId], references: [id])
  communityId String?
}

model MemberPurchase {
  id          String    @id @default(uuid())
  memberId    String
  communityId String
  productId   String // From Google/Apple
  platform    String // 'android' | 'ios'
  status      String // 'active' | 'expired' | 'revoked'
  purchasedAt DateTime  @default(now())
  expiresAt   DateTime?

  // Relations
  community       Community      @relation(fields: [communityId], references: [id])
  user            User           @relation(fields: [memberId], references: [id])
  MemberDetails   MemberDetails? @relation(fields: [memberDetailsId], references: [id])
  memberDetailsId String?

  @@unique([memberId, communityId])
  @@index([memberId])
  @@index([communityId])
  @@index([status])
  @@index([platform])
  @@index([expiresAt])
}

model Certificate {
  id     String @id @default(uuid())
  user   User   @relation(fields: [userId], references: [id])
  userId String
  title  String
  url    String
  // issuer      String?
  // issuedDate  DateTime?
  // expiryDate  DateTime?
  // description String?   @db.Text

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model User {
  id                         String    @id @unique @default(uuid())
  firebaseUid                String    @unique
  email                      String
  emailVerified              Boolean   @default(false)
  firstName                  String?
  lastName                   String?
  userType                   Json      @default("[]")
  currentType                USER_TYPE @default(MEMBER)
  profileImage               String?
  authType                   AUTH_TYPE
  countryCode                String?
  contact                    String?
  bio                        String?   @db.Text
  isPushNotificationsEnabled Boolean   @default(false)

  status          STATUS   @default(ENABLED)
  isDeleted       Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now()) @updatedAt
  profileComplete Boolean  @default(false)

  // Relations
  certificates      Certificate[]
  userSession       UserSession[]
  notification      Notification[]
  communities       Community[]
  chatChannelAccess ChatChannelAccess[]
  sentMessages      ChatMessage[]       @relation("UserSentMessages")
  MemberPurchase    MemberPurchase[]
}

model OtpVerification {
  id         String   @id @default(uuid())
  email      String
  otp        String
  expiresAt  DateTime
  isVerified Boolean  @default(false)

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([email, status])
  @@index([email, otp, status])
}

model UserSession {
  id       String  @id @unique @default(uuid())
  user     User    @relation(fields: [userId], references: [id])
  userId   String
  fcmToken String? @db.Text

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model Community {
  id              String           @id @unique @default(uuid())
  user            User             @relation(fields: [userId], references: [id])
  userId          String
  name            String
  description     String?          @db.Text
  image           String?
  banner          String?
  videoUrl        String?
  isFree          Boolean          @default(true)
  price           PriceModel?      @relation(fields: [priceId], references: [id])
  priceId         String?
  members         Int              @default(0)
  googleProductId String?
  appleProductId  String?
  communityStatus COMMUNITY_STATUS @default(PENDING)

  // MemberDetails fields
  memberDetails MemberDetails[] // Store member details as JSON for flexibility
  maxMembers    Int? // Maximum allowed members

  dailyPlans   DailyPlan[]
  chatChannels ChatChannel[]
  mediaLibrary MediaLibrary[]

  status         STATUS           @default(ENABLED)
  isDeleted      Boolean          @default(false)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @default(now()) @updatedAt
  MemberPurchase MemberPurchase[]

  @@index([userId])
  @@index([communityStatus])
  @@index([isFree])
  @@index([googleProductId])
  @@index([appleProductId])
  @@index([isDeleted])
}

model DailyPlan {
  id          String           @id @default(uuid())
  community   Community        @relation(fields: [communityId], references: [id])
  communityId String
  date        DateTime
  training    TrainingPlan[]
  nutrition   NutritionEntry[]
  other       OtherEntry[]
  logs        LogEntry[]

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model TrainingPlan {
  id          String       @id @default(uuid())
  dailyPlan   DailyPlan    @relation(fields: [dailyPlanId], references: [id])
  dailyPlanId String
  title       String
  description String?
  image       String?
  workoutType WORKOUT_TYPE @default(CUSTOM_WORKOUT)
  exercises   Exercise[]

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model Exercise {
  id             String       @id @default(uuid())
  trainingPlan   TrainingPlan @relation(fields: [trainingPlanId], references: [id])
  trainingPlanId String
  title          String
  image          String?
  sets           Int
  minSet         Int? // Minimum sets in range
  maxSet         Int? // Maximum sets in range
  repsType       REP_TYPE
  minReps        Int?
  maxReps        Int
  rpe            Int
  note           String?      @db.Text

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model NutritionEntry {
  id           String    @id @default(uuid())
  dailyPlan    DailyPlan @relation(fields: [dailyPlanId], references: [id])
  dailyPlanId  String
  name         String
  type         MEAL_TYPE
  ingredients  String    @db.Text
  recipe       String?   @db.Text
  mealImage    String?
  externalLink String?

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model OtherEntry {
  id          String    @id @default(uuid())
  dailyPlan   DailyPlan @relation(fields: [dailyPlanId], references: [id])
  dailyPlanId String
  title       String
  description String    @db.Text

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model LogEntry {
  id          String    @id @default(uuid())
  dailyPlan   DailyPlan @relation(fields: [dailyPlanId], references: [id])
  dailyPlanId String
  note        String    @db.Text
  videoUrl    String?

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model ChatChannel {
  id           String              @id @default(uuid())
  community    Community           @relation(fields: [communityId], references: [id])
  communityId  String
  name         String
  permission   CHANNEL_PERMISSION  @default(EVERYONE)
  allowedUsers ChatChannelAccess[]
  messages     ChatMessage[]

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model ChatChannelAccess {
  id        String      @id @default(uuid())
  channel   ChatChannel @relation(fields: [channelId], references: [id])
  channelId String
  user      User        @relation(fields: [userId], references: [id])
  userId    String

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model ChatMessage {
  id        String      @id @default(uuid())
  channel   ChatChannel @relation(fields: [channelId], references: [id])
  channelId String
  sender    User        @relation("UserSentMessages", fields: [senderId], references: [id])
  senderId  String
  content   String      @db.Text
  isRead    Boolean     @default(false)

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model MediaLibrary {
  id          String         @id @default(uuid())
  community   Community      @relation(fields: [communityId], references: [id])
  communityId String
  title       String
  category    MEDIA_CATEGORY
  videoUrl    String

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model Notification {
  id     String   @id @unique @default(uuid())
  type   String?  @db.Text
  title  String?  @db.Text
  body   String?  @db.Text
  isRead Boolean? @default(false)
  user   User?    @relation(fields: [userId], references: [id])
  userId String?

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}
