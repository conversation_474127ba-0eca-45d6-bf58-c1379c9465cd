import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { EmailService } from '../../shared/services/mailgun-email.service';
import * as admin from 'firebase-admin';
import { AUTH_TYPE, STATUS } from '@prisma/client';
import { UserSessionType } from 'src/shared/types/user-session.type';
import { generateNumericOtp } from '../../shared/module/bcrypt/bcrypt';
import { UserCoreService } from 'src/core/user-core/user-core.service';
import { UserSessionCoreService } from 'src/core/user-session-core/user-session-core.service';
import { OTPVerificationCoreService } from 'src/core/user-otp-verification-core/user-otp-verification-core.service';
import {
  VerifyEmailDto,
  SignupDto,
  LoginDto,
  SendVerificationEmailDto,
} from './dto/auth.dto';
import {
  authMessages,
  userMessage,
  userSessionMessage,
} from '../../shared/keys/helper.key';

@Injectable()
export class AuthService {
  constructor(
    private readonly userCoreService: UserCoreService,
    private readonly userSessionCoreService: UserSessionCoreService,
    private readonly otpVerificationCoreService: OTPVerificationCoreService,
    private emailService: EmailService,
  ) {}

  async validateTokenWithFirebase(headers: any) {
    if (!headers.authorization) {
      throw new UnauthorizedException(authMessages.AUTH_HEADER_NOT_FOUND);
    }

    const authHeaderValue = headers.authorization;

    if (!authHeaderValue.startsWith('Bearer')) {
      throw new UnauthorizedException(authMessages.AUTH_HEADER_IS_NOT_BEARER);
    }

    const parts = authHeaderValue.split(' ');

    if (parts.length !== 2) {
      throw new UnauthorizedException(authMessages.INVALID_AUTH_HEADER_BEARER);
    }

    const token = parts[1];

    try {
      const decodedToken = await admin.auth().verifyIdToken(token);
      return decodedToken;
    } catch {
      throw new UnauthorizedException(authMessages.TOKEN_EXPIRED);
    }
  }

  async performJWTStrategy(params: {
    request: Request & { headers: { authorization: string } };
  }): Promise<UserSessionType> {
    const { request } = params;

    const firebaseDecodedToken = await this.validateTokenWithFirebase(
      request.headers,
    );

    const user = await this.userCoreService.findFirst({
      where: {
        firebaseUid: firebaseDecodedToken.uid,
      },
    });

    if (!user) {
      throw new BadRequestException(authMessages.USER_NOT_FOUND);
    }
    if (user.isDeleted) {
      throw new BadRequestException(userMessage.USER_IS_DELETED);
    }
    if (user.status !== STATUS.ENABLED) {
      throw new BadRequestException(authMessages.ACCOUNT_DEACTIVATED);
    }

    return {
      user: user,
    };
  }

  async checkEmailExists(email: string) {
    const user = await this.userCoreService.findFirst({
      where: {
        email,
        isDeleted: false,
      },
    });

    const exists = !!user;

    const response: any = {
      exists,
      message: exists
        ? userMessage.USER_EMAIL_EXIST
        : userMessage.USER_EMAIL_NOT_EXIST,
    };

    if (exists) {
      response.authType = user.authType;
    }

    return response;
  }

  async signup(param: { signupDto: SignupDto; headers: any }) {
    const { signupDto, headers } = param;
    const { firstName, lastName, notificationToken, authType } = signupDto;
    try {
      // Get Firebase user data
      const firebaseDecodedToken =
        await this.validateTokenWithFirebase(headers);

      // Check if user already exists
      const existingUser = await this.userCoreService.findUnique({
        where: { firebaseUid: firebaseDecodedToken.uid },
      });

      if (existingUser) {
        throw new BadRequestException(userMessage.USER_ALREADY_EXIST);
      }

      // Check if email already exists with different Firebase UID
      if (firebaseDecodedToken.email) {
        const existingEmailUser = await this.userCoreService.findFirst({
          where: {
            email: firebaseDecodedToken.email,
            firebaseUid: { not: firebaseDecodedToken.uid },
            isDeleted: false,
          },
        });

        if (existingEmailUser) {
          throw new BadRequestException(authMessages.EMAIL_EXIST);
        }
      }

      // Check if email is verified (only if signup is via EMAIL)
      if (authType === AUTH_TYPE.EMAIL) {
        const verifiedOtp = await this.otpVerificationCoreService.findFirst({
          where: {
            email: firebaseDecodedToken.email,
            isVerified: true,
            isDeleted: true,
            status: STATUS.DISABLED,
          },
        });

        if (!verifiedOtp) {
          throw new BadRequestException(authMessages.EMAIL_NOT_VERIFIED);
        }
      }

      // Create new user
      const user = await this.userCoreService.create({
        data: {
          firebaseUid: firebaseDecodedToken.uid,
          email: firebaseDecodedToken.email || '',
          emailVerified: true,
          userType: ['MEMBER'],
          firstName,
          lastName,
          authType,
        },
      });

      // Create user session
      const userSession = await this.userSessionCoreService.create({
        data: {
          userId: user.id,
          fcmToken: notificationToken || null,
        },
      });

      return {
        status: true,
        message: 'User registered successfully',
        user,
        userSession,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(authMessages.AUTH_ERROR);
    }
  }

  async login(param: { loginDto: LoginDto; headers: any }) {
    const { loginDto, headers } = param;
    const { notificationToken } = loginDto;
    try {
      // Get Firebase user data
      const firebaseDecodedToken =
        await this.validateTokenWithFirebase(headers);

      // Check if user exists
      const user = await this.userCoreService.findUnique({
        where: { firebaseUid: firebaseDecodedToken.uid },
      });

      if (!user) {
        throw new BadRequestException(userMessage.USER_NOT_FOUND);
      }

      // Check if user is deleted or disabled
      if (user.isDeleted || user.status === STATUS.DISABLED) {
        throw new BadRequestException(authMessages.ACCOUNT_DEACTIVATED);
      }

      // Create or update user session
      let userSession = await this.userSessionCoreService.findFirst({
        where: { userId: user.id, status: STATUS.ENABLED, isDeleted: false },
      });

      if (!userSession) {
        userSession = await this.userSessionCoreService.create({
          data: {
            userId: user.id,
            fcmToken: notificationToken || null,
          },
        });
      } else if (notificationToken) {
        userSession = await this.userSessionCoreService.update({
          where: { id: userSession.id },
          data: { fcmToken: notificationToken },
        });
      }

      return {
        status: true,
        message: 'Login successful',
        user,
        userSession,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(authMessages.AUTH_ERROR);
    }
  }

  async sendVerificationEmail(
    sendVerificationEmailDto: SendVerificationEmailDto,
  ) {
    const { email } = sendVerificationEmailDto;

    // Check if email is already used by a verified user
    const existingUser = await this.userCoreService.findFirst({
      where: { email },
    });

    if (existingUser && existingUser.emailVerified) {
      throw new BadRequestException(authMessages.EMAIL_ALREADY_VERIFIED);
    }

    // Prevent frequent OTP requests
    const recentOtp = await this.otpVerificationCoreService.findFirst({
      where: {
        email,
        status: STATUS.ENABLED,
        createdAt: {
          gte: new Date(Date.now() - 50 * 1000), // last 50 seconds
        },
        isDeleted: false,
      },
    });

    if (recentOtp) {
      throw new BadRequestException(authMessages.OTP_REQUEST_TOO_FREQUENT);
    }

    // Soft-disable existing OTPs
    await this.otpVerificationCoreService.updateMany({
      where: {
        email,
        status: STATUS.ENABLED,
        isDeleted: false,
      },
      data: {
        status: STATUS.DISABLED,
        isDeleted: true,
      },
    });

    // Generate new OTP
    const otp = await generateNumericOtp();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes expiry

    // Save OTP
    await this.otpVerificationCoreService.create({
      data: {
        email,
        otp,
        expiresAt,
      },
    });

    // Send email
    const emailSent = await this.emailService.sendOtpEmail(email, otp);

    if (!emailSent) {
      // Soft-disable OTP if sending fails
      await this.otpVerificationCoreService.updateMany({
        where: {
          email,
          otp,
          status: STATUS.ENABLED,
          isDeleted: false,
        },
        data: {
          status: STATUS.DISABLED,
          isDeleted: true,
        },
      });

      throw new BadRequestException(authMessages.EMAIL_SEND_FAILED);
    }

    return {
      status: true,
      message: 'Verification email sent successfully',
    };
  }

  async verifyEmail(verifyEmailDto: VerifyEmailDto) {
    const otpRecord = await this.otpVerificationCoreService.findFirst({
      where: {
        email: verifyEmailDto.email,
        otp: verifyEmailDto.otp,
        status: STATUS.ENABLED,
        expiresAt: { gt: new Date() },
        isDeleted: false,
      },
    });

    if (!otpRecord) {
      throw new BadRequestException(authMessages.ENTER_VALID_OTP);
    }

    // Mark OTP as used and email as verified
    await this.otpVerificationCoreService.update({
      where: { id: otpRecord.id },
      data: {
        status: STATUS.DISABLED,
        isDeleted: true,
        isVerified: true,
      },
    });

    return {
      status: true,
      message: 'Email verified successfully',
    };
  }

  async logout(param: { sessionId: string; headers: any }) {
    const { sessionId, headers } = param;

    const firebaseDecodedToken = await this.validateTokenWithFirebase(headers);

    // Get the user based on Firebase UID
    const user = await this.userCoreService.findUnique({
      where: { firebaseUid: firebaseDecodedToken.uid, isDeleted: false },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    if (sessionId) {
      // Verify that the session belongs to the user
      const session = await this.userSessionCoreService.findUnique({
        where: { id: sessionId },
      });

      if (!session || session.userId !== user.id || session.isDeleted) {
        throw new BadRequestException(
          userSessionMessage.USER_SESSION_NOT_AUTHORIZED,
        );
      }

      // Mark the session as deleted
      await this.userSessionCoreService.update({
        where: { id: sessionId },
        data: { status: STATUS.DISABLED, isDeleted: true },
      });
    } else {
      // Logout all active sessions for the user
      await this.userSessionCoreService.updateMany({
        where: { userId: user.id, status: STATUS.ENABLED, isDeleted: false },
        data: { status: STATUS.DISABLED, isDeleted: true },
      });
    }

    return {
      status: true,
      message: authMessages.LOGOUT_SUCCESS,
    };
  }

  async deleteUser(headers: any) {
    // Validate Firebase token
    const firebaseDecodedToken = await this.validateTokenWithFirebase(headers);

    // Get user from local DB by Firebase UID
    const user = await this.userCoreService.findFirst({
      where: {
        firebaseUid: firebaseDecodedToken.uid,
        isDeleted: false,
      },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    // Delete from Firebase
    try {
      await admin.auth().deleteUser(user.firebaseUid);
    } catch (error: any) {
      throw new BadRequestException(
        error.message || 'Failed to delete user in Firebase',
      );
    }

    // Soft delete the user locally
    await this.userCoreService.update({
      where: { id: user.id },
      data: {
        isDeleted: true,
        status: STATUS.DISABLED,
      },
    });

    // Soft delete all sessions
    await this.userSessionCoreService.updateMany({
      where: {
        userId: user.id,
        isDeleted: false,
        status: STATUS.ENABLED,
      },
      data: {
        isDeleted: true,
        status: STATUS.DISABLED,
      },
    });

    return {
      status: true,
      message: 'User account deleted successfully',
    };
  }
}
