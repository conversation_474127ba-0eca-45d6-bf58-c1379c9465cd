import { Module } from '@nestjs/common';
import { IAPService } from 'src/shared/services/iap.service';
import { IAPController } from './iap.controller';
import { MemberPurchaseCoreModule } from 'src/core/member-purchase-core/member-purchase-core.module';
import { CommunityCoreModule } from 'src/core/community-core/community-core.module';
import { PriceModelCoreModule } from 'src/core/price-model-core/price-model-core.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MemberPurchaseCoreModule,
    CommunityCoreModule,
    PriceModelCoreModule,
    AuthModule
  ],
  controllers: [IAPController],
  providers: [IAPService],
  exports: [IAPService],
})
export class IAPModule {}
