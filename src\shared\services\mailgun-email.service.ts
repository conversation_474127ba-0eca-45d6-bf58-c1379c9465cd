import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as qs from 'qs';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  private readonly mailgunDomain: string;
  private readonly mailgunApiKey: string;
  private readonly mailgunSender: string;

  constructor(private configService: ConfigService) {
    this.mailgunDomain = this.configService.get('MAILGUN_PRODUCTION_DOMAIN')!;
    this.mailgunApiKey = this.configService.get('MAILGUN_API_PRODUCTION_KEY')!;
    this.mailgunSender = this.configService.get('MAILGUN_FROM_PRODUCTION_EMAIL')!;
  }

  async sendOtpEmail(email: string, otp: string): Promise<boolean> {
    const url = `https://api.mailgun.net/v3/${this.mailgunDomain}/messages`;

    const data = {
      from: `${this.configService.get('MAILGUN_FROM_PRODUCTION_NAME') || 'Junto'} <${this.mailgunSender}>`,
      to: email,
      subject: 'Email Verification - Junto',
      text: `Your verification code is: ${otp}. This code will expire in 10 minutes.`,
      html: this.getOtpEmailTemplate(otp),
    };

    try {
      const response = await axios.post(url, qs.stringify(data), {
        auth: {
          username: 'api',
          password: this.mailgunApiKey,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (response.status === 200) {
        this.logger.log(`OTP email sent successfully to ${email}`);
        return true;
      }

      this.logger.error(`Unexpected response:`, response.data);
      return false;
    } catch (error) {
      this.logger.error(`Failed to send OTP email to ${email}:`, error.response?.data || error.message);
      return false;
    }
  }

  private getOtpEmailTemplate(otp: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background: #f9f9f9; }
          .otp-code { font-size: 32px; font-weight: bold; color: #007bff; text-align: center; 
                     background: white; padding: 20px; margin: 20px 0; border-radius: 8px; 
                     letter-spacing: 5px; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
          .warning { color: #e74c3c; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Junto</h1>
            <h2>Email Verification</h2>
          </div>
          <div class="content">
            <h3>Hello User!</h3>
            <p>Thank you for signing up with Junto. To complete your registration, please verify your email address using the code below:</p>
            
            <div class="otp-code">${otp}</div>
            
            <p><strong>Important:</strong></p>
            <ul>
              <li>This verification code will expire in <span class="warning">10 minutes</span></li>
              <li>Do not share this code with anyone</li>
              <li>If you didn't request this verification, please ignore this email</li>
            </ul>
            
            <p>If you have any questions, feel free to contact our support team.</p>
          </div>
          <div class="footer">
            <p>&copy; 2025 Junto. All rights reserved.</p>
            <p>This is an automated email, please do not reply.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
