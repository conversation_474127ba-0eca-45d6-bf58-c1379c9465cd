import { Global, Module } from '@nestjs/common';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { MemberDetailsCoreService } from './member-details-core.service';
import { PrismaModule } from 'src/shared/module/prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [MemberDetailsCoreService, PrismaService],
  exports: [MemberDetailsCoreService],
})
export class MemberDetailsCoreModule {}
