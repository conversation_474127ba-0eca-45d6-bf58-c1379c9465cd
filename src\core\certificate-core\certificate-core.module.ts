import { Global, Module } from '@nestjs/common';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { CertificateCoreService } from './certificate-core.service';
import { PrismaModule } from 'src/shared/module/prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [CertificateCoreService, PrismaService],
  exports: [CertificateCoreService],
})
export class CertificateCoreModule {}
