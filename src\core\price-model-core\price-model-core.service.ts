import { Injectable } from '@nestjs/common';
import { PriceModel, Prisma } from '@prisma/client';
import { PrismaBaseRepository } from 'src/shared/libs/prisma-base.repository';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { PriceModelCorePaginateDto } from './dto/price-model-core.dto';
import { priceModelMessage } from 'src/shared/keys/helper.key';

@Injectable()
export class PriceModelCoreService extends PrismaBaseRepository<
  PriceModel,
  PriceModelCorePaginateDto,
  Prisma.PriceModelCreateArgs,
  Prisma.PriceModelUpdateArgs,
  Prisma.PriceModelUpdateManyArgs,
  Prisma.PriceModelFindUniqueArgs,
  Prisma.PriceModelFindFirstArgs,
  Prisma.PriceModelFindManyArgs,
  Prisma.PriceModelDeleteArgs,
  Prisma.PriceModelDeleteManyArgs,
  Prisma.PriceModelCountArgs
> {
  constructor(public prisma: PrismaService) {
    super(prisma.prisma.priceModel, {
      NOT_FOUND: priceModelMessage.PRICE_MODEL_NOT_FOUND,
      DELETED: priceModelMessage.PRICE_MODEL_IS_DELETED,
    });
  }

  async findByAmountAndInterval(amount: number, interval: string, currency: string = 'USD') {
    return this.findFirst({
      where: {
        amount: amount,
        interval: interval,
        currency: currency,
      },
    });
  }

  async createPriceModel(amount: number, interval: string, currency: string = 'USD') {
    // Check if price model already exists
    const existingPrice = await this.findByAmountAndInterval(amount, interval, currency);
    
    if (existingPrice) {
      return existingPrice;
    }

    return this.create({
      data: {
        amount: amount,
        currency: currency,
        interval: interval,
      },
    });
  }

  async getAllActivePriceModels() {
    return this.findMany({
      where: {
        status: 'ENABLED',
        isDeleted: false,
      },
      include: {
        communities: {
          where: {
            status: 'ENABLED',
            isDeleted: false,
          },
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }
}
