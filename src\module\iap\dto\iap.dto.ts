import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsNotEmpty } from 'class-validator';

export class ValidateReceiptDto {
  @ApiProperty({ description: 'Platform (android or ios)' })
  @IsEnum(['android', 'ios'])
  @IsNotEmpty()
  platform: 'android' | 'ios';

  @ApiProperty({ description: 'Product ID' })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ description: 'Receipt data or purchase token' })
  @IsString()
  @IsNotEmpty()
  receiptData: string;

  @ApiProperty({ description: 'Package name (required for Android)', required: false })
  @IsString()
  @IsOptional()
  packageName?: string;
}

export class CheckSubscriptionStatusDto {
  @ApiProperty({ description: 'Platform (android or ios)' })
  @IsEnum(['android', 'ios'])
  @IsNotEmpty()
  platform: 'android' | 'ios';

  @ApiProperty({ description: 'Product ID' })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ description: 'Purchase token or receipt data' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Package name (required for Android)', required: false })
  @IsString()
  @IsOptional()
  packageName?: string;
}
