# Subscription-Based IAP Integration Guide

## Overview
The system now creates **auto-renewable subscriptions** for both Google Play and Apple App Store, not in-app products. This is the correct approach for community memberships.

## Google Play Subscriptions

### Product Creation Structure
```typescript
const subscriptionData = {
  packageName,
  productId: productId,
  basePlans: [
    {
      basePlanId: 'monthly' | 'yearly',
      state: 'ACTIVE',
      autoRenewing: true,
      regionalConfigs: [
        {
          regionCode: '001', // Global region code
          price: {
            priceMicros: (price * 1000000).toString(),
            currencyCode: currency,
          },
        },
      ],
    },
  ],
  listings: {
    'en-US': {
      title,
      description,
    },
  },
};
```

### API Endpoint
- **Creation**: `applications.subscriptions.create`
- **Validation**: `purchases.subscriptions.get`
- **Management**: `purchases.subscriptions.cancel`, `purchases.subscriptions.defer`

## Apple App Store Subscriptions

### Product Creation Structure
```typescript
const subscriptionRequestBody = {
  data: {
    type: 'subscriptions',
    attributes: {
      productId: productId,
      referenceName: displayName,
      subscriptionPeriod: 'MONTHLY' | 'YEARLY',
      reviewNote: `Subscription for ${displayName}`,
      familySharable: false,
    },
    relationships: {
      app: {
        data: {
          type: 'apps',
          id: bundleId,
        },
      },
      subscriptionPricePoints: {
        data: [
          {
            type: 'subscriptionPricePoints',
            id: 'price-point-id',
          },
        ],
      },
    },
  },
};
```

### API Endpoint
- **Creation**: `POST https://api.appstoreconnect.apple.com/v1/subscriptions`
- **Validation**: Receipt validation with `latest_receipt_info`
- **Management**: App Store Connect API for subscription management

## Key Differences: Subscriptions vs In-App Products

### Subscriptions (✅ Current Implementation)
- **Auto-Renewable**: Automatically renew until cancelled
- **Recurring Revenue**: Monthly/yearly billing cycles
- **Grace Periods**: Handle failed payments gracefully
- **Family Sharing**: Can be configured for family plans
- **Webhook Support**: Rich notification system for status changes

### In-App Products (❌ Previous Approach)
- **One-Time Purchase**: Single payment, no renewal
- **Manual Management**: Requires manual renewal logic
- **Limited Webhooks**: Fewer notification types
- **No Grace Periods**: Immediate access loss on payment failure

## Subscription Lifecycle Management

### 1. Creation Flow
```
Community Created → Subscription Product Created → Auto-Approved → Available for Purchase
```

### 2. Purchase Flow
```
User Initiates Purchase → App Store Handles Payment → Receipt Validation → Access Granted
```

### 3. Renewal Flow
```
Auto-Renewal Attempt → Success/Failure Webhook → Update Database → Notify User
```

### 4. Cancellation Flow
```
User Cancels → Webhook Notification → Update Status → Access Until Expiry
```

## Webhook Handling

### Google Play Subscription Notifications
- **SUBSCRIPTION_RECOVERED**: Payment recovered after failure
- **SUBSCRIPTION_RENEWED**: Successful renewal
- **SUBSCRIPTION_CANCELED**: User cancelled subscription
- **SUBSCRIPTION_EXPIRED**: Subscription expired
- **SUBSCRIPTION_REVOKED**: Subscription revoked by Google

### Apple App Store Notifications
- **INITIAL_BUY**: First subscription purchase
- **RENEWAL**: Successful renewal
- **CANCEL**: User cancelled subscription
- **EXPIRED**: Subscription expired
- **GRACE_PERIOD_EXPIRED**: Grace period ended

## Database Schema for Subscriptions

### MemberPurchase Model
```prisma
model MemberPurchase {
  id                String   @id @default(cuid())
  memberId          String
  communityId       String
  status            PURCHASE_STATUS
  platform          PLATFORM
  receiptData       String?
  transactionId     String?
  productId         String?
  purchaseDate      DateTime
  expiresAt         DateTime?
  autoRenewing      Boolean  @default(true)
  subscriptionId    String?  // For subscription tracking
  originalTransactionId String? // For Apple subscription families
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}
```

## Testing Subscription Flow

### 1. Create Subscription Community
```bash
POST /community
{
  "name": "Premium Community",
  "description": "Monthly subscription community",
  "isFree": false,
  "priceAmount": 9.99,
  "priceCurrency": "USD",
  "priceInterval": "monthly"
}
```

### 2. Join with Subscription
```bash
POST /community/join
{
  "communityId": "community-id",
  "receiptData": "subscription-receipt-data",
  "platform": "android"
}
```

### 3. Check Subscription Status
```bash
GET /community/:id/subscription-status
```

## Production Considerations

### Google Play Console Setup
1. Create subscription products manually in Google Play Console
2. Configure base plans (monthly, yearly)
3. Set up regional pricing
4. Enable Real-time Developer Notifications (RTDN)

### Apple App Store Connect Setup
1. Create auto-renewable subscription groups
2. Configure subscription products within groups
3. Set up App Store Server Notifications
4. Configure subscription pricing tiers

## Error Handling

### Common Subscription Errors
- **Insufficient Permissions**: Service account lacks subscription management rights
- **Invalid Product ID**: Subscription product doesn't exist in store
- **Receipt Validation Failed**: Invalid or expired receipt
- **Network Timeout**: Store API temporarily unavailable

### Graceful Degradation
- Mock subscription creation for development
- Fallback to manual approval if auto-approval fails
- Retry mechanisms for webhook processing
- Comprehensive logging for debugging

The system now correctly handles auto-renewable subscriptions for both platforms, providing a robust foundation for community membership management!
