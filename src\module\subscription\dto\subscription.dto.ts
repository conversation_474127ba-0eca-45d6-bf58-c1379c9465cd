import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsNotEmpty } from 'class-validator';

export class RenewSubscriptionDto {
  @ApiProperty({ description: 'Platform (android or ios)' })
  @IsEnum(['android', 'ios'])
  @IsNotEmpty()
  platform: 'android' | 'ios';

  @ApiProperty({ description: 'Receipt data or purchase token' })
  @IsString()
  @IsNotEmpty()
  receiptData: string;

  @ApiProperty({ description: 'Package name (required for Android)', required: false })
  @IsString()
  @IsOptional()
  packageName?: string;
}
