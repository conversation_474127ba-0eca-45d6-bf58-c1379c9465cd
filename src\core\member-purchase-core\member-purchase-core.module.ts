import { Global, Module } from '@nestjs/common';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { MemberPurchaseCoreService } from './member-purchase-core.service';
import { PrismaModule } from 'src/shared/module/prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [MemberPurchaseCoreService, PrismaService],
  exports: [MemberPurchaseCoreService],
})
export class MemberPurchaseCoreModule {}
