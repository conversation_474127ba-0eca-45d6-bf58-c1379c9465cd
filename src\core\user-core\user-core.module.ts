import { Global, Module } from '@nestjs/common';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { UserCoreService } from './user-core.service';
import { PrismaModule } from 'src/shared/module/prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [UserCoreService, PrismaService],
  exports: [UserCoreService],
})
export class UserCoreModule {}
