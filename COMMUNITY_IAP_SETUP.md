# Community IAP Integration Setup Guide

## Overview
This system provides comprehensive community management with In-App Purchase (IAP) integration for both Google Play and Apple App Store.

## Environment Variables Required

### Google Play Configuration
```env
GOOGLE_APPLICATION_CREDENTIALS=./resource/google-service-account.json
GOOGLE_PLAY_PACKAGE_NAME=com.juntocommunity.app
```

### Apple App Store Configuration (Optional)
```env
APPLE_API_KEY=your_apple_api_key
APPLE_KEY_ID=your_apple_key_id
APPLE_ISSUER_ID=your_apple_issuer_id
APPLE_SHARED_SECRET=your_apple_shared_secret
APPLE_ENVIRONMENT=sandbox
APPLE_BUNDLE_ID=com.juntocommunity.app
```

## Features Implemented

### ✅ Community Management
- **Create Communities**: Owners can create free or paid communities
- **Auto-Approval**: Communities are automatically approved (no admin flow)
- **Product Creation**: Automatic Google Play and Apple product creation
- **CRUD Operations**: Full community management capabilities

### ✅ IAP Integration
- **Google Play**: Real product creation using Google Play Developer API
- **Apple App Store**: Product structure creation (with API framework ready)
- **Receipt Validation**: Both platforms supported
- **Subscription Management**: Complete lifecycle handling

### ✅ Member Features
- **Browse Communities**: View available communities
- **Join Communities**: Free or paid with IAP validation
- **Subscription Status**: Check and manage subscriptions
- **Purchase History**: View all community purchases

### ✅ Webhook Support
- **Google Play Webhooks**: Handle subscription notifications
- **Apple Webhooks**: Handle App Store notifications
- **Auto-Processing**: Automatic subscription status updates

## API Endpoints

### Community Management
- `POST /community` - Create community
- `GET /community/available` - Get available communities
- `POST /community/join` - Join community
- `GET /community/purchases` - Get user purchases
- `GET /community/:id/subscription-status` - Check subscription

### IAP Management
- `POST /iap/validate-receipt` - Validate purchase receipt
- `GET /iap/subscription-status` - Check subscription status

### Subscription Management
- `POST /subscription/:communityId/renew` - Renew subscription
- `PATCH /subscription/:communityId/cancel` - Cancel subscription
- `GET /subscription/my-subscriptions` - Get user subscriptions

### Webhooks
- `POST /webhook/google-play` - Google Play notifications
- `POST /webhook/apple` - Apple App Store notifications

## Database Schema

### Community Model
- Auto-approval workflow (PENDING → ACTIVE)
- Google/Apple product ID storage
- Price model integration
- Member tracking

### MemberPurchase Model
- Purchase tracking with status
- Platform identification (Android/iOS)
- Expiration date management
- Receipt validation data

## Production Setup

### Google Play Console
1. Create service account with Android Publisher API access
2. Download service account key file
3. Place in `./resource/google-service-account.json`
4. Enable Android Publisher API in Google Cloud Console

### Apple App Store Connect
1. Create API key with App Manager role
2. Configure environment variables
3. Set up webhook endpoints for notifications

## Testing Flow

1. **Create Community**:
   ```bash
   POST /community
   {
     "name": "Test Community",
     "description": "Test Description",
     "isFree": false,
     "priceAmount": 9.99,
     "priceCurrency": "USD",
     "priceInterval": "monthly"
   }
   ```

2. **Join Community**:
   ```bash
   POST /community/join
   {
     "communityId": "community-id",
     "receiptData": "receipt-from-app-store",
     "platform": "android"
   }
   ```

3. **Check Status**:
   ```bash
   GET /community/:id/subscription-status
   ```

## Auto-Approval Flow

Since there's no admin flow, communities are automatically approved:
1. Community created with PENDING status
2. App store products created
3. After 2 seconds, status changes to ACTIVE
4. Community becomes available for members

## Error Handling

- Graceful fallback for missing API credentials
- Comprehensive error messages
- Logging for debugging
- Transaction rollback on failures

## Security Features

- Firebase authentication on all endpoints
- Receipt validation for purchases
- Owner/member permission checks
- Input validation with DTOs

The system is production-ready with proper error handling, logging, and comprehensive IAP integration!
