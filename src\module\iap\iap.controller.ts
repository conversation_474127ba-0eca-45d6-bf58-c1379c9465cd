import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Query,
} from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { IAPService } from 'src/shared/services/iap.service';
import { UserLoginJwtGuard } from 'src/module/auth/guards/firebase-auth.guard';
import { GetUserSession } from 'src/shared/decorators/user-session.decorator';
import { UserSessionType } from 'src/shared/types/user-session.type';
import { ValidateReceiptDto, CheckSubscriptionStatusDto } from './dto/iap.dto';

@ApiTags('In-App Purchases')
@ApiBearerAuth()
@UseGuards(UserLoginJwtGuard)
@Controller('iap')
export class IAPController {
  constructor(private readonly iapService: IAPService) {}

  @Post('validate-receipt')
  @ApiOperation({ summary: 'Validate purchase receipt from Google Play or Apple App Store' })
  async validateReceipt(
    @Body() dto: ValidateReceiptDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    if (dto.platform === 'android') {
      return this.iapService.validateGooglePlayReceipt(
        dto.packageName!,
        dto.productId,
        dto.receiptData,
      );
    } else {
      return this.iapService.validateAppleReceipt(dto.receiptData);
    }
  }

  @Get('subscription-status')
  @ApiOperation({ summary: 'Check subscription status' })
  async checkSubscriptionStatus(
    @Query() dto: CheckSubscriptionStatusDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.iapService.checkSubscriptionStatus(
      dto.platform,
      dto.packageName || '',
      dto.productId,
      dto.token,
    );
  }
}
