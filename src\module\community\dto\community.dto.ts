import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsEnum,
  IsNotEmpty,
  Min,
} from 'class-validator';
import { COMMUNITY_STATUS } from '@prisma/client';

export class CreateCommunityDto {
  @ApiProperty({ description: 'Community name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Community description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Community image URL', required: false })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ description: 'Community banner URL', required: false })
  @IsString()
  @IsOptional()
  banner?: string;

  @ApiProperty({ description: 'Community video URL', required: false })
  @IsString()
  @IsOptional()
  videoUrl?: string;

  @ApiProperty({ description: 'Is community free', default: true })
  @IsBoolean()
  @IsOptional()
  isFree?: boolean = true;

  @ApiProperty({ description: 'Price amount (if not free)', required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  priceAmount?: number;

  @ApiProperty({ description: 'Price currency', default: 'USD', required: false })
  @IsString()
  @IsOptional()
  priceCurrency?: string = 'USD';

  @ApiProperty({ description: 'Price interval (monthly, yearly)', required: false })
  @IsString()
  @IsOptional()
  priceInterval?: string;

  @ApiProperty({ description: 'Maximum members allowed', required: false })
  @IsNumber()
  @IsOptional()
  @Min(1)
  maxMembers?: number;
}

export class UpdateCommunityDto {
  @ApiProperty({ description: 'Community name', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Community description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Community image URL', required: false })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ description: 'Community banner URL', required: false })
  @IsString()
  @IsOptional()
  banner?: string;

  @ApiProperty({ description: 'Community video URL', required: false })
  @IsString()
  @IsOptional()
  videoUrl?: string;

  @ApiProperty({ description: 'Is community free', required: false })
  @IsBoolean()
  @IsOptional()
  isFree?: boolean;

  @ApiProperty({ description: 'Price amount (if not free)', required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  priceAmount?: number;

  @ApiProperty({ description: 'Price currency', required: false })
  @IsString()
  @IsOptional()
  priceCurrency?: string;

  @ApiProperty({ description: 'Price interval (monthly, yearly)', required: false })
  @IsString()
  @IsOptional()
  priceInterval?: string;

  @ApiProperty({ description: 'Maximum members allowed', required: false })
  @IsNumber()
  @IsOptional()
  @Min(1)
  maxMembers?: number;
}

export class UpdateCommunityStatusDto {
  @ApiProperty({ description: 'Community status', enum: COMMUNITY_STATUS })
  @IsEnum(COMMUNITY_STATUS)
  @IsNotEmpty()
  status: COMMUNITY_STATUS;
}

export class JoinCommunityDto {
  @ApiProperty({ description: 'Community ID' })
  @IsString()
  @IsNotEmpty()
  communityId: string;

  @ApiProperty({ description: 'Platform (android or ios)', required: false })
  @IsEnum(['android', 'ios'])
  @IsOptional()
  platform?: 'android' | 'ios';

  @ApiProperty({ description: 'Purchase receipt/token (for paid communities)', required: false })
  @IsString()
  @IsOptional()
  receiptData?: string;

  @ApiProperty({ description: 'Package name (required for Android)', required: false })
  @IsString()
  @IsOptional()
  packageName?: string;
}