import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { google } from 'googleapis';
import * as appleReceiptVerify from 'node-apple-receipt-verify';
import * as path from 'path';

export interface GooglePlayProduct {
  productId: string;
  packageName: string;
  subscriptionId: string;
  basePlans: {
    basePlanId: string;
    state: string;
    autoRenewing: boolean;
    pricingPhases: {
      duration: string;
      price: {
        priceMicros: string;
        currencyCode: string;
      };
    }[];
  }[];
}

export interface AppleProduct {
  productId: string;
  bundleId: string;
  displayName: string;
  description: string;
  price: string;
  currency: string;
  subscriptionPeriod: string;
}

export interface ReceiptValidationResult {
  isValid: boolean;
  productId?: string;
  transactionId?: string;
  purchaseDate?: Date;
  expirationDate?: Date;
  platform: 'android' | 'ios';
  originalData?: any;
}

@Injectable()
export class IAPService {
  private readonly logger = new Logger(IAPService.name);
  private googlePlayPublisher: any;

  constructor(private configService: ConfigService) {
    this.initializeGooglePlay();
    this.initializeApple();
  }

  private async initializeGooglePlay() {
    try {
      const serviceAccountKey = this.configService.get('GOOGLE_APPLICATION_CREDENTIALS');
      if (serviceAccountKey) {
        const auth = new google.auth.GoogleAuth({
          keyFile: serviceAccountKey,
          scopes: ['https://www.googleapis.com/auth/androidpublisher'],
        });

        console.log('Google Play API initialized', auth);

        this.googlePlayPublisher = google.androidpublisher({
          version: 'v3',
          auth,
        });
      }
    } catch (error) {
      this.logger.error('Failed to initialize Google Play API:', error);
    }
  }

  private initializeApple() {
    try {
      const appleConfig = {
        secret: this.configService.get('APPLE_SHARED_SECRET'),
        environment: this.configService.get('APPLE_ENVIRONMENT') || 'sandbox',
        verbose: false,
        extended: true,
      };
      appleReceiptVerify.config(appleConfig);
    } catch (error) {
      this.logger.error('Failed to initialize Apple receipt verification:', error);
    }
  }

  async createGooglePlayProduct(
    packageName: string,
    productId: string,
    price: number,
    currency: string,
    subscriptionPeriod: string
  ): Promise<GooglePlayProduct | null> {
    try {
      // Note: Google Play subscriptions are typically created through Google Play Console
      // This method simulates the creation for development purposes
      // In production, you would create the subscription products manually in Google Play Console
      // and then use the product IDs here

      this.logger.warn(`Google Play product creation simulated for: ${productId}`);
      this.logger.warn('In production, create subscription products through Google Play Console');

      // Return a mock product structure for development
      const mockProduct: GooglePlayProduct = {
        productId: productId,
        packageName: packageName,
        subscriptionId: productId,
        basePlans: [
          {
            basePlanId: subscriptionPeriod === 'monthly' ? 'monthly' : 'yearly',
            state: 'ACTIVE',
            autoRenewing: true,
            pricingPhases: [
              {
                duration: subscriptionPeriod,
                price: {
                  priceMicros: (price * 1000000).toString(),
                  currencyCode: currency,
                },
              },
            ],
          },
        ],
      };

      this.logger.log(`Google Play product structure created: ${productId}`);
      return mockProduct;
    } catch (error) {
      this.logger.error(`Failed to create Google Play product ${productId}:`, error);
      return null;
    }
  }

  async createAppleProduct(
    bundleId: string,
    productId: string,
    displayName: string,
    description: string,
    price: string,
    currency: string,
    subscriptionPeriod: string
  ): Promise<AppleProduct | null> {
    try {
      // Note: Apple products are typically created through App Store Connect
      // This is a placeholder for the product structure
      const appleProduct: AppleProduct = {
        productId,
        bundleId,
        displayName,
        description,
        price,
        currency,
        subscriptionPeriod,
      };

      this.logger.log(`Apple product structure created: ${productId}`);
      return appleProduct;
    } catch (error) {
      this.logger.error(`Failed to create Apple product ${productId}:`, error);
      return null;
    }
  }

  async validateGooglePlayReceipt(
    packageName: string,
    productId: string,
    purchaseToken: string
  ): Promise<ReceiptValidationResult> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      const response = await this.googlePlayPublisher.purchases.subscriptions.get({
        packageName: packageName,
        subscriptionId: productId,
        token: purchaseToken,
      });

      const purchase = response.data;
      const isValid = purchase.paymentState === 1; // 1 = Received
      const expirationDate = purchase.expiryTimeMillis 
        ? new Date(parseInt(purchase.expiryTimeMillis))
        : undefined;

      return {
        isValid,
        productId,
        transactionId: purchase.orderId,
        purchaseDate: purchase.startTimeMillis 
          ? new Date(parseInt(purchase.startTimeMillis))
          : undefined,
        expirationDate,
        platform: 'android',
        originalData: purchase,
      };
    } catch (error) {
      this.logger.error('Google Play receipt validation failed:', error);
      return {
        isValid: false,
        platform: 'android',
      };
    }
  }

  async validateAppleReceipt(receiptData: string): Promise<ReceiptValidationResult> {
    try {
      const result = await appleReceiptVerify.validate({
        receipt: receiptData,
        device: false,
      });

      if (result && result.receipt) {
        const latestReceiptInfo = result.latest_receipt_info?.[0] || result.receipt.in_app?.[0];
        
        return {
          isValid: true,
          productId: latestReceiptInfo?.product_id,
          transactionId: latestReceiptInfo?.transaction_id,
          purchaseDate: latestReceiptInfo?.purchase_date_ms 
            ? new Date(parseInt(latestReceiptInfo.purchase_date_ms))
            : undefined,
          expirationDate: latestReceiptInfo?.expires_date_ms 
            ? new Date(parseInt(latestReceiptInfo.expires_date_ms))
            : undefined,
          platform: 'ios',
          originalData: result,
        };
      }

      return {
        isValid: false,
        platform: 'ios',
      };
    } catch (error) {
      this.logger.error('Apple receipt validation failed:', error);
      return {
        isValid: false,
        platform: 'ios',
      };
    }
  }

  async checkSubscriptionStatus(
    platform: 'android' | 'ios',
    packageName: string,
    productId: string,
    token: string
  ): Promise<{ isActive: boolean; expirationDate?: Date }> {
    try {
      if (platform === 'android') {
        const result = await this.validateGooglePlayReceipt(packageName, productId, token);
        return {
          isActive: result.isValid && result.expirationDate ? result.expirationDate > new Date() : false,
          expirationDate: result.expirationDate,
        };
      } else {
        const result = await this.validateAppleReceipt(token);
        return {
          isActive: result.isValid && result.expirationDate ? result.expirationDate > new Date() : false,
          expirationDate: result.expirationDate,
        };
      }
    } catch (error) {
      this.logger.error('Subscription status check failed:', error);
      return { isActive: false };
    }
  }
}
