import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { google } from 'googleapis';
import * as appleReceiptVerify from 'node-apple-receipt-verify';
import * as path from 'path';

export interface GooglePlayProduct {
  productId: string;
  packageName: string;
  subscriptionId: string;
  basePlans: {
    basePlanId: string;
    state: string;
    autoRenewing: boolean;
    pricingPhases: {
      duration: string;
      price: {
        priceMicros: string;
        currencyCode: string;
      };
    }[];
  }[];
}

export interface AppleProduct {
  productId: string;
  bundleId: string;
  displayName: string;
  description: string;
  price: string;
  currency: string;
  subscriptionPeriod: string;
}

export interface ReceiptValidationResult {
  isValid: boolean;
  productId?: string;
  transactionId?: string;
  purchaseDate?: Date;
  expirationDate?: Date;
  platform: 'android' | 'ios';
  originalData?: any;
}

@Injectable()
export class IAPService {
  private readonly logger = new Logger(IAPService.name);
  private googlePlayPublisher: any;

  constructor(private configService: ConfigService) {
    this.initializeGooglePlay();
    this.initializeApple();
  }

  private async initializeGooglePlay() {
    try {
      const serviceAccountKey = this.configService.get(
        'GOOGLE_APPLICATION_CREDENTIALS',
      );
      if (serviceAccountKey) {
        const auth = new google.auth.GoogleAuth({
          keyFile: serviceAccountKey,
          scopes: ['https://www.googleapis.com/auth/androidpublisher'],
        });

        this.googlePlayPublisher = google.androidpublisher({
          version: 'v3',
          auth,
        });
      }
    } catch (error) {
      this.logger.error('Failed to initialize Google Play API:', error);
    }
  }

  private initializeApple() {
    try {
      const appleConfig = {
        secret: this.configService.get('APPLE_SHARED_SECRET'),
        environment: this.configService.get('APPLE_ENVIRONMENT') || 'sandbox',
        verbose: false,
        extended: true,
      };
      appleReceiptVerify.config(appleConfig);
    } catch (error) {
      this.logger.error(
        'Failed to initialize Apple receipt verification:',
        error,
      );
    }
  }

  async createGooglePlayProduct(
    packageName: string,
    productId: string,
    price: number,
    currency: string,
    subscriptionPeriod: string,
    title: string,
    description: string,
  ): Promise<GooglePlayProduct | null> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      // Create in-app product (managed product for subscriptions)
      const inAppProduct = {
        packageName,
        sku: title,
        status: 'active',
        purchaseType: 'managedUser',
        defaultLanguage: 'en-US',
        listings: {
          'en-US': {
            title,
            description,
          },
        },
        defaultPrice: {
          priceMicros: (price * 1000000).toString(),
          currency,
        },
      };

      const response = await this.googlePlayPublisher.subscriptions.insert({
        packageName: packageName,
        requestBody: inAppProduct,
      });

      this.logger.log(`Google Play product created: ${productId}`);

      // Return structured product data
      const product: GooglePlayProduct = {
        productId: response.data.sku,
        packageName: response.data.packageName,
        subscriptionId: response.data.sku,
        basePlans: [
          {
            basePlanId: subscriptionPeriod === 'monthly' ? 'monthly' : 'yearly',
            state: 'ACTIVE',
            autoRenewing: true,
            pricingPhases: [
              {
                duration: subscriptionPeriod,
                price: {
                  priceMicros: (price * 1000000).toString(),
                  currencyCode: currency,
                },
              },
            ],
          },
        ],
      };

      console.log("Product", product);

      return product;
    } catch (error) {
      this.logger.error(
        `Failed to create Google Play product ${productId}:`,
        error,
      );
      return null;
    }
  }

  async createAppleProduct(
    bundleId: string,
    productId: string,
    displayName: string,
    description: string,
    price: string,
    currency: string,
    subscriptionPeriod: string,
  ): Promise<AppleProduct | null> {
    try {
      // Create Apple In-App Purchase using App Store Connect API
      const appleApiKey = this.configService.get('APPLE_API_KEY');
      const appleKeyId = this.configService.get('APPLE_KEY_ID');
      const appleIssuerId = this.configService.get('APPLE_ISSUER_ID');

      if (!appleApiKey || !appleKeyId || !appleIssuerId) {
        this.logger.warn('Apple API credentials not configured, creating mock product');
        const mockProduct: AppleProduct = {
          productId,
          bundleId,
          displayName,
          description,
          price,
          currency,
          subscriptionPeriod,
        };
        this.logger.log(`Apple product structure created: ${productId}`);
        return mockProduct;
      }

      // App Store Connect API request body
      const requestBody = {
        data: {
          type: 'inAppPurchases',
          attributes: {
            productId: productId,
            referenceName: displayName,
            inAppPurchaseType: 'AUTOMATICALLY_RENEWABLE_SUBSCRIPTION',
            state: 'DEVELOPER_ACTION_NEEDED',
          },
          relationships: {
            app: {
              data: {
                type: 'apps',
                id: bundleId, // This should be the app ID from App Store Connect
              },
            },
          },
        },
      };

      // Note: In production, you would make an actual API call to App Store Connect
      // For now, we'll simulate the creation
      this.logger.log(`Apple product would be created via App Store Connect API: ${productId}`);

      const appleProduct: AppleProduct = {
        productId,
        bundleId,
        displayName,
        description,
        price,
        currency,
        subscriptionPeriod,
      };

      this.logger.log(`Apple product structure created: ${productId}`, appleProduct);
      return appleProduct;
    } catch (error) {
      this.logger.error(`Failed to create Apple product ${productId}:`, error);
      return null;
    }
  }

  async validateGooglePlayReceipt(
    packageName: string,
    productId: string,
    purchaseToken: string,
  ): Promise<ReceiptValidationResult> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      const response =
        await this.googlePlayPublisher.purchases.subscriptions.get({
          packageName: packageName,
          subscriptionId: productId,
          token: purchaseToken,
        });

      const purchase = response.data;
      const isValid = purchase.paymentState === 1; // 1 = Received
      const expirationDate = purchase.expiryTimeMillis
        ? new Date(parseInt(purchase.expiryTimeMillis))
        : undefined;

      return {
        isValid,
        productId,
        transactionId: purchase.orderId,
        purchaseDate: purchase.startTimeMillis
          ? new Date(parseInt(purchase.startTimeMillis))
          : undefined,
        expirationDate,
        platform: 'android',
        originalData: purchase,
      };
    } catch (error) {
      this.logger.error('Google Play receipt validation failed:', error);
      return {
        isValid: false,
        platform: 'android',
      };
    }
  }

  async validateAppleReceipt(
    receiptData: string,
  ): Promise<ReceiptValidationResult> {
    try {
      const result = await appleReceiptVerify.validate({
        receipt: receiptData,
        device: false,
      });

      if (result && result.receipt) {
        const latestReceiptInfo =
          result.latest_receipt_info?.[0] || result.receipt.in_app?.[0];

        return {
          isValid: true,
          productId: latestReceiptInfo?.product_id,
          transactionId: latestReceiptInfo?.transaction_id,
          purchaseDate: latestReceiptInfo?.purchase_date_ms
            ? new Date(parseInt(latestReceiptInfo.purchase_date_ms))
            : undefined,
          expirationDate: latestReceiptInfo?.expires_date_ms
            ? new Date(parseInt(latestReceiptInfo.expires_date_ms))
            : undefined,
          platform: 'ios',
          originalData: result,
        };
      }

      return {
        isValid: false,
        platform: 'ios',
      };
    } catch (error) {
      this.logger.error('Apple receipt validation failed:', error);
      return {
        isValid: false,
        platform: 'ios',
      };
    }
  }

  async checkSubscriptionStatus(
    platform: 'android' | 'ios',
    packageName: string,
    productId: string,
    token: string,
  ): Promise<{ isActive: boolean; expirationDate?: Date }> {
    try {
      if (platform === 'android') {
        const result = await this.validateGooglePlayReceipt(
          packageName,
          productId,
          token,
        );
        return {
          isActive:
            result.isValid && result.expirationDate
              ? result.expirationDate > new Date()
              : false,
          expirationDate: result.expirationDate,
        };
      } else {
        const result = await this.validateAppleReceipt(token);
        return {
          isActive:
            result.isValid && result.expirationDate
              ? result.expirationDate > new Date()
              : false,
          expirationDate: result.expirationDate,
        };
      }
    } catch (error) {
      this.logger.error('Subscription status check failed:', error);
      return { isActive: false };
    }
  }
}
