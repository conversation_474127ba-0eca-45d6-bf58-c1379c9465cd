import { Injectable } from '@nestjs/common';
import { MemberDetails, Prisma } from '@prisma/client';
import { PrismaBaseRepository } from 'src/shared/libs/prisma-base.repository';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { MemberDetailsCorePaginateDto } from './dto/member-details-core.dto';
import { memberDetailsMessage } from 'src/shared/keys/helper.key';

@Injectable()
export class MemberDetailsCoreService extends PrismaBaseRepository<
  MemberDetails,
  MemberDetailsCorePaginateDto,
  Prisma.MemberDetailsCreateArgs,
  Prisma.MemberDetailsUpdateArgs,
  Prisma.MemberDetailsUpdateManyArgs,
  Prisma.MemberDetailsFindUniqueArgs,
  Prisma.MemberDetailsFindFirstArgs,
  Prisma.MemberDetailsFindManyArgs,
  Prisma.MemberDetailsDeleteArgs,
  Prisma.MemberDetailsDeleteManyArgs,
  Prisma.MemberDetailsCountArgs
> {
  constructor(public prisma: PrismaService) {
    super(prisma.prisma.memberDetails, {
      NOT_FOUND: memberDetailsMessage.MEMBER_DETAILS_NOT_FOUND,
      DELETED: memberDetailsMessage.MEMBER_DETAILS_IS_DELETED,
    });
  }

  async findByUserId(userId: string) {
    return this.findFirst({
      where: {
        userId: userId,
      },
      include: {
        memberPurchases: {
          include: {
            community: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    profileImage: true,
                  },
                },
                price: true,
              },
            },
          },
        },
      },
    });
  }

  async findMembersByCommunity(communityId: string) {
    return this.findMany({
      where: {
        communityId: communityId,
      },
      include: {
        memberPurchases: {
          where: {
            communityId: communityId,
          },
        },
      },
    });
  }

  async createOrUpdateMemberDetails(userId: string, communityId: string, membershipType?: string) {
    const existingMember = await this.findFirst({
      where: {
        userId: userId,
        communityId: communityId,
      },
    });

    if (existingMember) {
      return this.update({
        where: { id: existingMember.id },
        data: {
          membershipType: membershipType || existingMember.membershipType,
        },
      });
    } else {
      return this.create({
        data: {
          userId: userId,
          communityId: communityId,
          membershipType: membershipType,
        },
      });
    }
  }
}
